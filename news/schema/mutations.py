import logging

import graphene
from django.conf import settings
from django.core.mail import EmailMessage
from django.template.loader import render_to_string
from gabbro.graphene import BadRequest

from common.utils.request_form_validation import validate_contact_request
from news.schema.input_object_types import ContactUsInput
from news.schema.object_types import ContactUsType
from news.serializers import ContactUsSerializer

logger = logging.getLogger("news")


class ContactUs(graphene.Mutation):
    contact_us = graphene.Field(ContactUsType)

    class Input:
        data_input = graphene.Argument(ContactUsInput)

    @validate_contact_request
    def mutate(self, info, data_input):
        serializer = ContactUsSerializer(data=data_input)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        contact_us = serializer.save()

        template = "email/email.html"
        context = {"contact": contact_us}
        subject = (
            f"New Sales Lead: {contact_us.subject}"
            if contact_us.subject
            else f"New Contact Form Submission from {contact_us.name}"
        )
        to_emails = getattr(settings, "TO_EMAILS", [])

        message_html = render_to_string(template, context)
        email_message = EmailMessage(subject=subject, body=message_html, to=to_emails)
        email_message.content_subtype = "html"
        try:
            email_message.send()
        except Exception as e:
            logger.debug(
                f"[Contact Us] contact_us_id: {contact_us.id}, Failed to send email: {e}"
            )

        return ContactUs(contact_us=contact_us)


class Mutation(graphene.ObjectType):
    contact_us = ContactUs.Field()

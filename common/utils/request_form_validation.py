import logging
from functools import wraps
from typing import Any, Dict, Optional

import requests
from django.conf import settings
from django.core.exceptions import ValidationError
from gabbro.graphene import Forbidden
from google.cloud import recaptchaenterprise_v1
from google.cloud.recaptchaenterprise_v1 import Assessment

logger = logging.getLogger("news")

GC_PROJECT_ID = getattr(settings, "GC_PROJECT_ID", None)
RECAPTCHA_VERIFY_URL = getattr(settings, "RECAPTCHA_VERIFY_URL", None)
RECAPTCHA_SECRET_KEY = getattr(settings, "RECAPTCHA_SECRET_KEY", None)
RECAPTCHA_MIN_SCORE = getattr(settings, "RECAPTCHA_MIN_SCORE", None)


def validate_csrf(request) -> None:
    """
    Validate that the CSRF token in the header matches the cookie.
    Raises ValidationError on failure.
    """
    header_token = request.headers.get("HTTP_X_CSRF_TOKEN")
    cookie_token = request.COOKIES.get("x-csrf-token")

    if not header_token:
        raise ValidationError({"csrf": "Missing X-CSRF-Token header."})
    if not cookie_token:
        raise ValidationError({"csrf": "Missing x-csrf-token cookie."})
    if header_token != cookie_token:
        raise ValidationError({"csrf": "CSRF token mismatch."})


def verify_recaptcha(token: Optional[str]) -> Dict[str, Any]:
    """
    Verify reCAPTCHA token with Google. Returns the parsed JSON response.
    """
    # ... existing code ...
    if not token:
        return {"success": False, "error-codes": ["missing-input-response"]}

    if not RECAPTCHA_SECRET_KEY:
        logger.warning("RECAPTCHA_SECRET_KEY is not configured in settings.")
        return {"success": False, "error-codes": ["missing-secret"]}

    try:
        resp = requests.post(
            RECAPTCHA_VERIFY_URL,
            data={"secret": RECAPTCHA_SECRET_KEY, "response": token},
            timeout=5,
        )
        resp.raise_for_status()
        return resp.json()
    except Exception as e:
        logger.exception("reCAPTCHA verification failed: %s", e)
        return {"success": False, "error-codes": ["recaptcha-not-reachable"]}


def validate_recaptcha(token: Optional[str]) -> None:
    """
    Validate that reCAPTCHA succeeded and meets the score threshold.
    Raises ValidationError on failure.
    """
    # ... existing code ...
    result = verify_recaptcha(token)
    if not result.get("success", False):
        detail = result.get("error-codes") or ["recaptcha-verification-failed"]
        raise ValidationError({"recaptcha": f"Verification failed: {detail}"})

    score = result.get("score")
    if score is None:
        raise ValidationError({"recaptcha": "Missing reCAPTCHA score."})
    if score < RECAPTCHA_MIN_SCORE:
        raise ValidationError({"recaptcha": f"Low score ({score}); suspected bot."})


def validate_contact_request(func):
    @wraps(func)
    def wrapper(self, info, data_input):
        """
        Composite validator performing:
          1) CSRF validation
          2) reCAPTCHA verification (score > 0.5)
        Raises ValidationError on failure.
        """
        recaptcha_token = data_input.get("recaptcha_token")
        try:
            validate_csrf(info.context)
            validate_recaptcha(recaptcha_token)
        except ValidationError as e:
            raise Forbidden(reason=e.message_dict)
        return func(self, info, data_input)

    return wrapper


def create_assessment(
        project_id: str, recaptcha_key: str, token: str
) -> Optional[Assessment]:
    """Create an assessment to analyze the risk of a UI action.
    Args:
        project_id: Your Google Cloud Project ID.
        recaptcha_key: The reCAPTCHA key associated with the site/app
        token: The generated token obtained from the client.
    """

    client = recaptchaenterprise_v1.RecaptchaEnterpriseServiceClient()

    # Set the properties of the event to be tracked.
    event = recaptchaenterprise_v1.Event()
    event.site_key = recaptcha_key
    event.token = token

    assessment = recaptchaenterprise_v1.Assessment()
    assessment.event = event

    project_name = f"projects/{project_id}"

    # Build the assessment request.
    request = recaptchaenterprise_v1.CreateAssessmentRequest()
    request.assessment = assessment
    request.parent = project_name

    response = client.create_assessment(request)

    # Check if the token is valid.
    if not response.token_properties.valid:
        logger.debug(
            "The CreateAssessment call failed because the token was "
            + "invalid for the following reasons: "
            + str(response.token_properties.invalid_reason)
        )
        raise ValidationError({"recaptcha": "Invalid token."})

    logger.debug(
        "The reCAPTCHA score for this token is: " + str(response.risk_analysis.score)
    )
    # Get the assessment name (id). Use this to annotate the assessment.
    assessment_name = client.parse_assessment_path(response.name).get("assessment")
    logger.debug(f"Assessment name: {assessment_name}")
    return response

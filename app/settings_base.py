import os  # noqa

from gabbro.settings import *  # noqa
from gabbro.settings import INSTALLED_APPS as GABBRO_INSTALLED_APPS, MIDDLEWARE

PROJECT_APPS = [
    # include gabbro for global gabbro locale messages
    "gabbro",
    # Access control list based on Django Guardian
    "gabbro.acl",
    # app to handle images and attachments
    "gabbro.uploads",
]
CORE_APPS = ["users", "news"]
INSTALLED_APPS = GABBRO_INSTALLED_APPS + PROJECT_APPS + CORE_APPS

MIDDLEWARE = ["django.middleware.gzip.GZipMiddleware", *MIDDLEWARE]

INTERNAL_MODE = True if os.getenv("INTERNAL_MODE", "False") == "True" else False
NOTEBOOK_ARGUMENTS = [
    "--ip",
    "0.0.0.0",
    "--port",
    "8888",
    "--allow-root",
    "--no-browser",
]

# LOGGING
LOGGING_LOGGERS_DEFAULTS = {
    "propagate": True,
    "level": "DEBUG",
}
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default_formatter": {
            "format": "%(asctime)s - [%(name)s] - %(message)s",
        },
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "formatter": "default_formatter",
            "class": "logging.StreamHandler",
        },
        "structured_log_handler": {
            "class": "google.cloud.logging.handlers.StructuredLogHandler",
            "level": "INFO",
        },
    },
    "loggers": {
        app: {**LOGGING_LOGGERS_DEFAULTS, "handlers": ["console"]}
        for app in INSTALLED_APPS
    },
}

EMAIL_BACKEND = os.getenv(
    "EMAIL_BACKEND", "django.core.mail.backends.smtp.EmailBackend"
)
EMAIL_HOST = os.getenv("EMAIL_HOST", None)
EMAIL_HOST_USER = os.getenv("EMAIL_HOST_USER", None)
EMAIL_HOST_PASSWORD = os.getenv("EMAIL_HOST_PASSWORD", None)
EMAIL_PORT = os.getenv("EMAIL_PORT", 587)
DEFAULT_FROM_EMAIL = os.getenv("DEFAULT_FROM_EMAIL", None)
TO_EMAILS = ["<EMAIL>"]

RECAPTCHA_VERIFY_URL = os.getenv(
    "RECAPTCHA_VERIFY_URL", "https://www.google.com/recaptcha/api/siteverify"
)
RECAPTCHA_SECRET_KEY = os.getenv("RECAPTCHA_SECRET_KEY", None)
RECAPTCHA_MIN_SCORE = float(os.getenv("RECAPTCHA_MIN_SCORE", 0.5))
